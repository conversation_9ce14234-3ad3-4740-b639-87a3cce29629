// Custom hook for Trading Session Management

import { useCallback, useEffect, useRef } from 'react';
import { useTradingSessionStore } from '@/stores';
import { useErrorHandler } from '@/stores/errorStore';
import { CSVParseResult, CandleData } from '@/types/trading';
import { UseTradingSessionReturn } from '@/types/stores';
import { TimeframeAggregator } from '@/utils/timeframeAggregator';
import { getBidAskFromCandle, getBidAskFromTick } from '@/utils/tradingCalculations';

export const useTradingSession = (): UseTradingSessionReturn => {
  const store = useTradingSessionStore();
  const errorHandler = useErrorHandler();
  const playbackIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeframeAggregatorRef = useRef<TimeframeAggregator | null>(null);

  // Data loading and processing
  const handleDataLoaded = useCallback((result: CSVParseResult) => {
    const loadingId = 'data-processing';

    errorHandler.handleSyncError(() => {
      errorHandler.startLoading(loadingId, 'Processing data...');

      let candleData: CandleData[] = [];
      let baseData: CandleData[] = [];

      if (result.dataType === 'candle') {
        candleData = result.candleData || [];
        baseData = candleData;

        if (candleData.length === 0) {
          throw new Error('No candle data found in the imported file');
        }
      } else if (result.dataType === 'tick' && result.tickData) {
        errorHandler.updateLoading(loadingId, 'Aggregating tick data to candles...', 25);

        // Create timeframe aggregator for tick data
        timeframeAggregatorRef.current = new TimeframeAggregator();

        errorHandler.updateLoading(loadingId, 'Generating M1 candles...', 50);

        // Generate M1 candles from tick data as base
        baseData = timeframeAggregatorRef.current.aggregateToTimeframe(result.tickData, 'M1');
        candleData = baseData;

        if (baseData.length === 0) {
          throw new Error('Failed to generate candles from tick data');
        }
      } else {
        throw new Error('Invalid data format or missing data');
      }

      errorHandler.updateLoading(loadingId, 'Finalizing...', 75);

      // Set precision based on data
      const precision = result.precision || 5;

      // Update store with new data
      store.setData(candleData, baseData, result.tickData);
      store.setSymbol(result.symbol || 'Unknown');
      store.setDataType(result.dataType);
      store.setPrecision(precision);
      store.setPlaybackSpeed(result.dataType === 'tick' ? 100 : 1000);
      store.setShowImporter(false);

      errorHandler.stopLoading(loadingId);
      errorHandler.showSuccess(
        'Data Loaded Successfully',
        `Loaded ${candleData.length} ${result.dataType === 'tick' ? 'candles from tick data' : 'candles'} for ${result.symbol || 'Unknown'}`
      );
    }, 'Data Loading Failed');
  }, [store, errorHandler]);

  // Timeframe switching
  const switchTimeframe = useCallback((newTimeframe: string) => {
    if (!timeframeAggregatorRef.current || !store.tickData) {
      // For regular candle data, just update timeframe
      store.setTimeframe(newTimeframe as any);
      return;
    }

    // For tick data, aggregate to new timeframe
    const aggregatedData = timeframeAggregatorRef.current.aggregateToTimeframe(
      store.tickData,
      newTimeframe as any
    );
    
    store.setData(aggregatedData, store.baseData, store.tickData);
    store.setTimeframe(newTimeframe as any);
  }, [store]);

  // Playback control
  const startPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
    }

    store.setIsPlaying(true);

    const speed = store.playbackSpeed === 'realtime' ? 100 : store.playbackSpeed;
    
    playbackIntervalRef.current = setInterval(() => {
      const currentState = useTradingSessionStore.getState();
      const maxIndex = Math.max(0, currentState.data.length - 1);
      
      if (currentState.currentIndex >= maxIndex) {
        stopPlayback();
        return;
      }
      
      store.stepForward();
    }, speed);
  }, [store]);

  const stopPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
      playbackIntervalRef.current = null;
    }
    store.setIsPlaying(false);
  }, [store]);

  const togglePlayback = useCallback(() => {
    if (store.isPlaying) {
      stopPlayback();
    } else {
      startPlayback();
    }
  }, [store.isPlaying, startPlayback, stopPlayback]);

  // Market data updates
  const updateCurrentMarketData = useCallback(() => {
    const currentData = store.data[store.currentIndex];
    if (!currentData) return;

    let bid: number, ask: number, spread: number;

    if (store.dataType === 'tick' && store.tickData) {
      // For tick data, get bid/ask from current tick
      const currentTick = store.tickData[store.currentIndex];
      if (currentTick) {
        const bidAsk = getBidAskFromTick(currentTick);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      } else {
        // Fallback to candle data
        const bidAsk = getBidAskFromCandle(currentData);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      }
    } else {
      // For candle data
      const bidAsk = getBidAskFromCandle(currentData);
      bid = bidAsk.bid;
      ask = bidAsk.ask;
      spread = bidAsk.spread;
    }

    store.updateMarketData(bid, ask, spread);
  }, [store]);

  // Update market data when current index changes
  useEffect(() => {
    updateCurrentMarketData();
  }, [store.currentIndex, updateCurrentMarketData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current);
      }
    };
  }, []);

  // Computed values
  const totalItems = store.dataType === 'tick' && store.tickData 
    ? store.tickData.length 
    : store.data.length;

  const hasData = store.data.length > 0 || (store.tickData?.length || 0) > 0;

  const canStepForward = store.currentIndex < Math.max(0, totalItems - 1);
  const canStepBackward = store.currentIndex > 0;

  return {
    // State
    ...store,
    
    // Computed values
    totalItems,
    hasData,
    canStepForward,
    canStepBackward,
    
    // Actions
    handleDataLoaded,
    switchTimeframe,
    startPlayback,
    stopPlayback,
    togglePlayback,
    updateCurrentMarketData
  };
};

// Custom hook for Account Management

import { useCallback, useEffect } from 'react';
import { useAccountStore } from '@/stores';
import { useErrorHandler } from '@/stores/errorStore';
import { Order, OrderType } from '@/types/trading';
import { UseAccountReturn } from '@/types/stores';
import { 
  calculateCommission,
  canExecuteOrder,
  validateOrderPrice,
  shouldTriggerPendingOrder,
  getExecutionPrice,
  checkStopLossTakeProfitBidAsk,
  calculateUnrealizedPnLBidAsk,
  calculatePerformanceMetrics
} from '@/utils/tradingCalculations';

interface PlaceOrderParams {
  type: OrderType;
  size: number;
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  currentBid: number;
  currentAsk: number;
  precision: number;
}

export const useAccount = (): UseAccountReturn => {
  const store = useAccountStore();
  const errorHandler = useErrorHandler();

  // Order placement logic
  const placeOrder = useCallback((params: PlaceOrderParams) => {
    return errorHandler.handleSyncError(() => {
      const { type, size, price, stopLoss, takeProfit, currentBid, currentAsk, precision } = params;

      // Validate order
      if (!canExecuteOrder(store.account, size)) {
        throw new Error('Insufficient margin to place order');
      }

      // Determine execution price
      const executionPrice = getExecutionPrice(type, currentBid, currentAsk, price);

      // Validate price levels
      if (price && !validateOrderPrice(type, price, currentBid, currentAsk)) {
        throw new Error('Invalid order price');
      }

      // Calculate commission
      const commission = calculateCommission(size, store.account.balance);

      // Create order
      const newOrder: Order = {
        id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        size,
        price: price || executionPrice,
        stopLoss,
        takeProfit,
        timestamp: Date.now(),
        status: price ? 'pending' : 'executed',
        commission
      };

      if (price) {
        // Pending order
        store.addOrder(newOrder);
        errorHandler.showInfo(
          'Pending Order Placed',
          `${type.toUpperCase()} ${size} lots at ${price.toFixed(precision)}`
        );
      } else {
        // Market order - execute immediately
        store.processOrderExecution(newOrder, executionPrice);
        errorHandler.showSuccess(
          'Order Executed',
          `${type.toUpperCase()} ${size} lots at ${executionPrice.toFixed(precision)}`
        );
      }

      return newOrder;
    }, 'Order Placement Failed') || {} as Order;
  }, [store, errorHandler]);

  // Position management
  const closePosition = useCallback((positionId: string, currentBid: number, currentAsk: number) => {
    const position = store.positions.find(p => p.id === positionId);
    if (!position) return;

    // Determine close price based on position type
    const closePrice = position.type === 'buy' ? currentBid : currentAsk;
    
    // Calculate final PnL
    const priceDiff = position.type === 'buy' 
      ? closePrice - position.openPrice
      : position.openPrice - closePrice;
    const realizedPnL = priceDiff * position.size * 100000; // Assuming forex lot size

    // Update account balance
    const newBalance = store.account.balance + realizedPnL - position.commission;
    store.updateAccount({ 
      balance: newBalance,
      totalPnL: store.account.totalPnL + realizedPnL
    });

    // Remove position
    store.removePosition(positionId);
  }, [store]);

  // Update position SL/TP
  const updatePositionLevels = useCallback((
    positionId: string, 
    stopLoss?: number, 
    takeProfit?: number,
    currentBid: number,
    currentAsk: number
  ) => {
    const position = store.positions.find(p => p.id === positionId);
    if (!position) return;

    // Validate SL/TP levels
    const currentPrice = position.type === 'buy' ? currentBid : currentAsk;
    
    if (stopLoss !== undefined) {
      const isValidSL = position.type === 'buy' 
        ? stopLoss < currentPrice 
        : stopLoss > currentPrice;
      
      if (!isValidSL) {
        throw new Error('Invalid stop loss level');
      }
    }

    if (takeProfit !== undefined) {
      const isValidTP = position.type === 'buy' 
        ? takeProfit > currentPrice 
        : takeProfit < currentPrice;
      
      if (!isValidTP) {
        throw new Error('Invalid take profit level');
      }
    }

    store.updatePosition(positionId, { stopLoss, takeProfit });
  }, [store]);

  // Check and execute pending orders
  const checkPendingOrders = useCallback((currentBid: number, currentAsk: number) => {
    const pendingOrders = store.orders.filter(order => order.status === 'pending');
    
    pendingOrders.forEach(order => {
      if (shouldTriggerPendingOrder(order, currentBid, currentAsk)) {
        const executionPrice = getExecutionPrice(order.type, currentBid, currentAsk);
        store.processOrderExecution(order, executionPrice);
      }
    });
  }, [store]);

  // Check SL/TP triggers
  const checkStopLossTakeProfit = useCallback((currentBid: number, currentAsk: number) => {
    store.positions.forEach(position => {
      const shouldClose = checkStopLossTakeProfitBidAsk(position, currentBid, currentAsk);
      if (shouldClose) {
        closePosition(position.id, currentBid, currentAsk);
      }
    });
  }, [store.positions, closePosition]);

  // Update all position PnLs
  const updateAllPositionPnLs = useCallback((currentBid: number, currentAsk: number) => {
    store.positions.forEach(position => {
      const unrealizedPnL = calculateUnrealizedPnLBidAsk(position, currentBid, currentAsk);
      store.updatePosition(position.id, { unrealizedPnL });
    });
  }, [store]);

  // Reset account to initial state
  const resetAccount = useCallback((initialBalance: number = 10000) => {
    store.resetAccount(initialBalance);
  }, [store]);

  // Performance metrics
  const performanceMetrics = useCallback(() => {
    return calculatePerformanceMetrics(store.orders);
  }, [store.orders]);

  // Computed values
  const totalUnrealizedPnL = store.positions.reduce((sum, pos) => sum + (pos.unrealizedPnL || 0), 0);
  const totalCommission = store.orders.reduce((sum, order) => sum + (order.commission || 0), 0);
  const equity = store.account.balance + totalUnrealizedPnL;
  const usedMargin = store.positions.reduce((sum, pos) => sum + (pos.size * 1000), 0); // Simplified margin calc
  const freeMargin = Math.max(0, equity - usedMargin);
  const marginLevel = usedMargin > 0 ? (equity / usedMargin) * 100 : 0;

  // Update account with computed values
  useEffect(() => {
    store.updateAccount({
      equity,
      margin: usedMargin,
      freeMargin,
      marginLevel,
      totalCommission
    });
  }, [equity, usedMargin, freeMargin, marginLevel, totalCommission, store]);

  return {
    // State
    account: store.account,
    orders: store.orders,
    positions: store.positions,
    startBalance: store.startBalance,
    
    // Computed values
    totalUnrealizedPnL,
    performanceMetrics: performanceMetrics(),
    
    // Actions
    placeOrder,
    closePosition,
    updatePositionLevels,
    checkPendingOrders,
    checkStopLossTakeProfit,
    updateAllPositionPnLs,
    resetAccount,
    
    // Store actions (direct access)
    addOrder: store.addOrder,
    updateOrder: store.updateOrder,
    removeOrder: store.removeOrder,
    addPosition: store.addPosition,
    updatePosition: store.updatePosition,
    removePosition: store.removePosition
  };
};

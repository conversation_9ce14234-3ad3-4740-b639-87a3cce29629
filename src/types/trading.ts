// Trading data types for MetaTrader CSV format
export interface CandleData {
  date: string;
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  tickVol: number;
  vol: number;
  spread: number;
  timestamp: number; // Unix timestamp for easier processing
}

// Tick data types for MetaTrader tick CSV format
export interface TickData {
  date: string;
  time: string;
  bid?: number;
  ask?: number;
  last?: number;
  volume?: number;
  flags: number; // 2=BID, 4=ASK, 6=BID+ASK
  timestamp: number; // Unix timestamp with milliseconds
}

// Data type enum
export type DataType = 'candle' | 'tick';

// Combined data interface
export interface MarketData {
  type: DataType;
  candles?: CandleData[];
  ticks?: TickData[];
}

// Chart data format for lightweight-charts
export interface ChartCandleData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
}

// Order types
export type OrderType = 'buy' | 'sell';
export type OrderStatus = 'pending' | 'filled' | 'cancelled' | 'closed' | 'open';

// Order execution types for pending orders
export type OrderExecutionType = 'market' | 'buyLimit' | 'sellLimit' | 'buyStop' | 'sellStop';

export interface OrderExecutionTypeInfo {
  value: OrderExecutionType;
  label: string;
  description: string;
  requiresPrice: boolean; // Whether this order type requires a specific execution price
}

export interface Order {
  id: string;
  type: OrderType;
  symbol: string;
  size: number; // Lot size
  entryPrice: number; // For market orders, this is the actual execution price; for pending orders, this is the trigger price
  executionPrice?: number; // For pending orders, this is the price at which the order should be executed
  orderExecutionType: OrderExecutionType; // How the order should be executed
  stopLoss?: number;
  takeProfit?: number;
  timestamp: number;
  status: OrderStatus;
  exitPrice?: number;
  exitTimestamp?: number;
  pnl?: number;
  commission?: number;
}

// Position (open trade)
export interface Position {
  id: string;
  orderId: string;
  type: OrderType;
  symbol: string;
  size: number;
  entryPrice: number;
  currentPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  entryTimestamp: number;
  unrealizedPnL: number;
  commission: number; // Entry commission
  exitCommission?: number; // Exit commission (when closed)
}

// Account state
export interface Account {
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  totalPnL: number;
  totalCommission: number;
}

// Drag and drop interfaces for order management
export enum DragLineType {
  PENDING_ORDER = 'pending_order',
  POSITION_ENTRY = 'position_entry',
  STOP_LOSS = 'stop_loss',
  TAKE_PROFIT = 'take_profit'
}

export interface LineMetadata {
  id: string; // Order ID or Position ID
  type: DragLineType;
  orderId?: string; // For pending orders
  positionId?: string; // For position-related lines
  originalPrice: number; // Original price before drag
  isValid: boolean; // Whether current drag position is valid
}

export interface DragState {
  isDragging: boolean;
  draggedLineId: string | null;
  draggedLineType: DragLineType | null;
  startPrice: number;
  currentPrice: number;
  startY: number; // Screen coordinate where drag started
  currentY: number; // Current screen coordinate
  isValidPosition: boolean; // Whether current drag position is valid
  dragDirection: 'up' | 'down' | null; // Direction of drag from start
}

export interface DragToleranceConfig {
  lineProximity: number; // Pixels tolerance for line detection
  minDragDistance: number; // Minimum pixels to start drag
  priceSnapTolerance: number; // Price tolerance for snapping
}

export interface DragCallbacks {
  onPendingOrderPriceUpdate?: (orderId: string, newPrice: number) => Promise<boolean>;
  onPositionStopLossUpdate?: (positionId: string, newStopLoss: number | null) => Promise<boolean>;
  onPositionTakeProfitUpdate?: (positionId: string, newTakeProfit: number | null) => Promise<boolean>;
  onDragStart?: (lineId: string, lineType: DragLineType) => void;
  onDragEnd?: (lineId: string, lineType: DragLineType, success: boolean) => void;
  onDragValidationChange?: (isValid: boolean, errorMessage?: string) => void;
}

// Trading settings
export interface TradingSettings {
  commissionPerLot: number; // USD per lot
  leverage: number;
  marginRequirement: number; // Percentage (e.g., 0.01 for 1%)
}

// Trading session state (DEPRECATED - now split into separate stores)
// This interface is kept for backward compatibility but should not be used in new code
export interface TradingSession {
  symbol: string;
  data: CandleData[];
  baseData: CandleData[];
  tickData?: TickData[];
  dataType: DataType;
  currentIndex: number;
  isPlaying: boolean;
  playbackSpeed: number | 'realtime';
  orders: Order[];
  positions: Position[];
  account: Account;
  startBalance: number;
  timeframe: Timeframe;
  updateMode: UpdateMode;
  currentBid: number;
  currentAsk: number;
  spread: number;
  lastKnownBid: number;
  lastKnownAsk: number;
  precision?: number;
  settings: TradingSettings;
}

// CSV parsing result
export interface CSVParseResult {
  candleData?: CandleData[]; // Renamed from 'data' for clarity
  tickData?: TickData[];
  dataType: DataType;
  errors: string[];
  symbol?: string;
  precision?: number; // Auto-detected decimal precision from the data
}

// Store-specific types for better type safety
export interface StoreAction<T = any> {
  type: string;
  payload?: T;
}

export interface StoreState {
  lastUpdated: number;
  version: number;
}

// Hook return types for better IntelliSense
export interface TradingSessionHookReturn {
  // State
  symbol: string;
  data: CandleData[];
  baseData: CandleData[];
  tickData?: TickData[];
  dataType: DataType;
  currentIndex: number;
  isPlaying: boolean;
  playbackSpeed: number | 'realtime';
  currentBid: number;
  currentAsk: number;
  spread: number;
  lastKnownBid: number;
  lastKnownAsk: number;
  precision?: number;
  timeframe: Timeframe;
  updateMode: UpdateMode;
  settings: TradingSettings;
  showImporter: boolean;
  showSettings: boolean;

  // Computed values
  totalItems: number;
  hasData: boolean;
  canStepForward: boolean;
  canStepBackward: boolean;

  // Actions
  handleDataLoaded: (result: CSVParseResult) => void;
  switchTimeframe: (timeframe: string) => void;
  startPlayback: () => void;
  stopPlayback: () => void;
  togglePlayback: () => void;
  updateCurrentMarketData: () => void;
  stepForward: () => void;
  stepBackward: () => void;
  resetSession: () => void;
  setShowImporter: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
  updateSettings: (settings: Partial<TradingSettings>) => void;
}

export interface AccountHookReturn {
  // State
  account: Account;
  orders: Order[];
  positions: Position[];
  startBalance: number;

  // Computed values
  totalUnrealizedPnL: number;
  performanceMetrics: PerformanceMetrics;

  // Actions
  placeOrder: (params: {
    type: OrderType;
    size: number;
    price?: number;
    stopLoss?: number;
    takeProfit?: number;
    currentBid: number;
    currentAsk: number;
    precision: number;
  }) => Order;
  closePosition: (positionId: string, currentBid: number, currentAsk: number) => void;
  updatePositionLevels: (positionId: string, stopLoss?: number, takeProfit?: number, currentBid?: number, currentAsk?: number) => void;
  checkPendingOrders: (currentBid: number, currentAsk: number) => void;
  checkStopLossTakeProfit: (currentBid: number, currentAsk: number) => void;
  updateAllPositionPnLs: (currentBid: number, currentAsk: number) => void;
  resetAccount: (initialBalance?: number) => void;
}

// Timeframe types
export type Timeframe = 'S1' | 'S5' | 'S10' | 'S15' | 'S30' | 'M1' | 'M5' | 'M15' | 'M30' | 'H1' | 'H4' | 'D1' | 'W1' | 'MN1';

export interface TimeframeInfo {
  value: Timeframe;
  label: string;
  seconds: number; // Changed from minutes to seconds for more precision
}

// Update mode types
export type UpdateMode = 'complete' | 'intraCandle';

export interface UpdateModeInfo {
  value: UpdateMode;
  label: string;
  description: string;
}

// Chart settings
export interface ChartSettings {
  showVolume: boolean;
  showGrid: boolean;
  theme: 'light' | 'dark';
  timeframe: Timeframe;
}

// Data feed manager interface
export interface DataFeedState {
  currentIndex: number;
  visibleData: CandleData[];
  bufferSize: number;
  timeframe: Timeframe;
  updateMode: UpdateMode;
  intraCandleStep: number; // Current step within the candle (0 to steps-1)
  intraCandleSteps: number; // Total steps per candle for current timeframe
}

// Performance metrics
export interface PerformanceMetrics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalPnL: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
}

// Bid/Ask price information
export interface BidAskPrice {
  bid: number;
  ask: number;
  spread: number;
  timestamp: number;
  mid?: number; // Calculated mid-price for convenience
}

// Price quote for order execution
export interface PriceQuote {
  buy: number;  // Price to buy at (ask)
  sell: number; // Price to sell at (bid)
  spread: number;
  timestamp: number;
}

// Order validation result
export interface OrderValidationResult {
  isValid: boolean;
  error?: string;
}

// Chart interaction types for order placement
export interface ChartOrderPlacement {
  price: number;
  time: number;
  orderExecutionType: OrderExecutionType;
  orderType: OrderType;
  size: number;
  stopLoss?: number;
  takeProfit?: number;
}

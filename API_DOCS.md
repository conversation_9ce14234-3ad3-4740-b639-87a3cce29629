# API Documentation - FX Backtester

## 🎣 Custom Hooks API

### useTradingSession()

Manages trading session lifecycle, data operations, and playback controls.

#### Returns: `UseTradingSessionReturn`

```typescript
interface UseTradingSessionReturn {
  // State
  symbol: string;
  data: CandleData[];
  baseData: CandleData[];
  tickData?: TickData[];
  dataType: 'candle' | 'tick';
  currentIndex: number;
  isPlaying: boolean;
  playbackSpeed: number | 'realtime';
  currentBid: number;
  currentAsk: number;
  spread: number;
  precision?: number;
  timeframe: Timeframe;
  updateMode: UpdateMode;
  settings: TradingSettings;
  showImporter: boolean;
  showSettings: boolean;
  
  // Computed values
  totalItems: number;
  hasData: boolean;
  canStepForward: boolean;
  canStepBackward: boolean;
  
  // Actions
  handleDataLoaded: (result: CSVParseResult) => void;
  switchTimeframe: (timeframe: string) => void;
  startPlayback: () => void;
  stopPlayback: () => void;
  togglePlayback: () => void;
  stepForward: () => void;
  stepBackward: () => void;
  resetSession: () => void;
  updateCurrentMarketData: () => void;
  setShowImporter: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
  updateSettings: (settings: Partial<TradingSettings>) => void;
}
```

#### Usage Examples

```typescript
const tradingSession = useTradingSession();

// Load trading data
tradingSession.handleDataLoaded({
  candleData: candleArray,
  dataType: 'candle',
  symbol: 'EURUSD',
  precision: 5,
  errors: []
});

// Control playback
tradingSession.startPlayback();
tradingSession.setPlaybackSpeed(500); // 500ms between candles
tradingSession.stepForward();

// Switch timeframes
tradingSession.switchTimeframe('M5');

// Check state
if (tradingSession.hasData && tradingSession.canStepForward) {
  tradingSession.stepForward();
}
```

### useAccount()

Handles trading account operations, order management, and performance tracking.

#### Returns: `UseAccountReturn`

```typescript
interface UseAccountReturn {
  // State
  account: Account;
  orders: Order[];
  positions: Position[];
  startBalance: number;
  
  // Computed values
  totalUnrealizedPnL: number;
  performanceMetrics: PerformanceMetrics;
  
  // Actions
  placeOrder: (params: PlaceOrderParams) => Order;
  closePosition: (positionId: string, currentBid: number, currentAsk: number) => void;
  updatePositionLevels: (positionId: string, stopLoss?: number, takeProfit?: number, currentBid?: number, currentAsk?: number) => void;
  checkPendingOrders: (currentBid: number, currentAsk: number) => void;
  checkStopLossTakeProfit: (currentBid: number, currentAsk: number) => void;
  updateAllPositionPnLs: (currentBid: number, currentAsk: number) => void;
  resetAccount: (initialBalance?: number) => void;
}
```

#### Usage Examples

```typescript
const account = useAccount();

// Place a market order
const order = account.placeOrder({
  type: 'buy',
  size: 0.1,
  currentBid: 1.1300,
  currentAsk: 1.1302,
  precision: 5
});

// Place a pending order
const pendingOrder = account.placeOrder({
  type: 'buy-limit',
  size: 0.1,
  price: 1.1250,
  stopLoss: 1.1200,
  takeProfit: 1.1350,
  currentBid: 1.1300,
  currentAsk: 1.1302,
  precision: 5
});

// Update position levels
account.updatePositionLevels(
  'position-id',
  1.1200, // New stop loss
  1.1400, // New take profit
  1.1320, // Current bid
  1.1322  // Current ask
);

// Close position
account.closePosition('position-id', 1.1320, 1.1322);

// Check account state
console.log('Balance:', account.account.balance);
console.log('Equity:', account.account.equity);
console.log('Total PnL:', account.totalUnrealizedPnL);
console.log('Performance:', account.performanceMetrics);
```

### useChartInteraction(config?)

Manages chart interactions, drawing tools, and user interface events.

#### Parameters

```typescript
interface UseChartInteractionProps {
  onChartOrderPlace?: (orderData: ChartOrderPlacement) => void;
  dragCallbacks?: DragCallbacks;
}
```

#### Returns: `UseChartInteractionReturn`

```typescript
interface UseChartInteractionReturn {
  // State
  fibonacciLines: FibonacciLine[];
  trendLines: TrendLine[];
  isDragging: boolean;
  contextMenu: ContextMenuState;
  enableFibonacci: boolean;
  enableDragAndDrop: boolean;
  
  // Chart management
  setChartRef: (chart: IChartApi | null) => void;
  chartRef: IChartApi | null;
  
  // Event handlers
  handleRightClick: (param: MouseEventParams) => void;
  handleContextMenuAction: (action: string, price: number, time: number) => void;
  setupChartEventHandlers: (chart: IChartApi) => () => void;
  
  // Drawing tools
  addFibonacciRetracement: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  addTrendLine: (startPrice: number, endPrice: number, startTime: number, endTime: number) => void;
  clearAllDrawings: () => void;
  
  // Drag and drop
  startDrag: (orderId?: string, positionId?: string) => void;
  endDrag: () => void;
}
```

#### Usage Examples

```typescript
const chartInteraction = useChartInteraction({
  onChartOrderPlace: (orderData) => {
    account.placeOrder({
      type: orderData.orderType,
      size: orderData.size,
      price: orderData.price,
      currentBid: tradingSession.currentBid,
      currentAsk: tradingSession.currentAsk,
      precision: tradingSession.precision || 5
    });
  },
  dragCallbacks: {
    onDragStart: (lineId, lineType) => {
      console.log('Started dragging:', lineId, lineType);
    },
    onDragEnd: (lineId, lineType, success) => {
      console.log('Drag ended:', lineId, lineType, success);
    }
  }
});

// Set chart reference
chartInteraction.setChartRef(chartInstance);

// Add drawing tools
chartInteraction.addFibonacciRetracement(1.1300, 1.1400, startTime, endTime);
chartInteraction.addTrendLine(1.1250, 1.1350, startTime, endTime);

// Handle chart events
const cleanup = chartInteraction.setupChartEventHandlers(chartInstance);

// Cleanup when component unmounts
useEffect(() => cleanup, []);
```

## 🏪 Store API

### Trading Session Store

Direct store access for advanced use cases:

```typescript
import { useTradingSessionStore } from '@/stores';

const store = useTradingSessionStore();

// Actions
store.setData(candleData, baseData, tickData);
store.setCurrentIndex(100);
store.setIsPlaying(true);
store.updateMarketData(1.1300, 1.1302, 0.0002);

// State
console.log(store.symbol);
console.log(store.currentIndex);
console.log(store.isPlaying);
```

### Account Store

```typescript
import { useAccountStore } from '@/stores';

const store = useAccountStore();

// Actions
store.addOrder(order);
store.processOrderExecution(order, executionPrice);
store.updatePosition(positionId, { unrealizedPnL: 50 });

// State
console.log(store.account);
console.log(store.orders);
console.log(store.positions);
```

### Error Store

```typescript
import { useErrorStore, useErrorHandler } from '@/stores';

// Direct store access
const errorStore = useErrorStore();
errorStore.showSuccess('Success', 'Operation completed');
errorStore.startLoading('operation-id', 'Processing...');

// Enhanced error handler
const errorHandler = useErrorHandler();
const result = await errorHandler.handleAsyncError(
  async () => {
    // Async operation
    return await someAsyncFunction();
  },
  'Operation Failed',
  'Loading data...'
);
```

## 📊 Type Definitions

### Core Trading Types

```typescript
interface CandleData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface TickData {
  timestamp: number;
  bid: number;
  ask: number;
  last: number;
  volume: number;
}

interface Order {
  id: string;
  type: OrderType;
  size: number;
  entryPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  timestamp: number;
  status: 'pending' | 'executed' | 'cancelled';
  commission?: number;
}

interface Position {
  id: string;
  orderId: string;
  type: 'buy' | 'sell';
  size: number;
  entryPrice: number;
  entryTime: number;
  stopLoss?: number;
  takeProfit?: number;
  unrealizedPnL: number;
  commission: number;
  swap: number;
}

interface Account {
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  totalPnL: number;
  totalCommission: number;
}
```

### Order Types

```typescript
type OrderType = 
  | 'buy' 
  | 'sell' 
  | 'buy-limit' 
  | 'sell-limit' 
  | 'buy-stop' 
  | 'sell-stop';

type Timeframe = 
  | 'S1' | 'S5' | 'S10' | 'S15' | 'S30'
  | 'M1' | 'M5' | 'M15' | 'M30'
  | 'H1' | 'H4'
  | 'D1' | 'W1' | 'MN1';

type UpdateMode = 'complete' | 'intracandle';
type DataType = 'candle' | 'tick';
```

## 🔧 Utility Functions

### Trading Calculations

```typescript
import { 
  calculateCommission,
  canExecuteOrder,
  validateOrderPrice,
  getExecutionPrice,
  calculateUnrealizedPnL,
  calculatePerformanceMetrics
} from '@/utils/tradingCalculations';

// Calculate commission for order
const commission = calculateCommission(0.1, 10000); // size, balance

// Check if order can be executed
const canExecute = canExecuteOrder(account, 0.1); // account, size

// Validate order price
const isValid = validateOrderPrice('buy-limit', 1.1250, 1.1300, 1.1302);

// Get execution price for order
const price = getExecutionPrice('buy', 1.1300, 1.1302, 1.1250);

// Calculate position PnL
const pnl = calculateUnrealizedPnL(position, 1.1350);

// Calculate performance metrics
const metrics = calculatePerformanceMetrics(orders);
```

## 🚨 Error Handling

All hooks include comprehensive error handling:

```typescript
// Automatic error handling in hooks
const tradingSession = useTradingSession();

try {
  tradingSession.handleDataLoaded(invalidData);
} catch (error) {
  // Error is automatically handled and displayed to user
  // No need for manual error handling in most cases
}

// Manual error handling for custom scenarios
const errorHandler = useErrorHandler();

const result = errorHandler.handleSyncError(() => {
  // Potentially failing operation
  return riskyOperation();
}, 'Custom Error Title');

if (result === null) {
  // Operation failed, error was displayed to user
}
```

This API documentation provides comprehensive coverage of all public interfaces in the FX Backtester application. For implementation details and examples, refer to the test files and component usage throughout the codebase.
